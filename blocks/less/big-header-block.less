// out: false
.bigHeaderBlock {
    .topCols {
        margin-bottom: @vw90;
        .col {
            display: inline-block;
            vertical-align: top;
            width: 55%;
            &:first-child {
                width: 45%;
            }
        }
    }
    .bigMediaWrapper {
        padding: @vw100 * 2.7 0 @vw100 + @vw40 0;
        position: relative;
        overflow: hidden;
        .background {
            position: absolute;
            top: -10%;
            left: 0;
            width: 100%;
            height: 120%;
            overflow: hidden;
            &:after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: @primaryColor;
                opacity: .5;
            }
            video, img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
        .subTitle {
            margin-bottom: @vw30;
        }
        .normalTitle {
            &:not(.overlayText) {
                opacity: .4;
            }
            &.overlayText {
                position: absolute;
                pointer-events: none;
                top: 0;
                left: 0;
                .line {
                width: 0%;
                white-space: nowrap;
                }
            }
        }
    }
    .contentWrapper {
        .innerWrapper {
            padding-left: 45%;
        }
    }
    .extraText {
        margin-top: @vw100 + @vw40;
        padding-right: (@vw106 * 2) + (@vw16 * 2);
    }
    .images {
        .imageWrapper {
            display: inline-block;
            vertical-align: bottom;
            &:first-child {
                margin-bottom: @vw100 + @vw80;
                width: (@vw106 * 4) + (@vw16 * 3);
                .innerImage {
                    .paddingRatio(472,577);
                }
            }
            &:last-child {
                width: (@vw106 * 2) + @vw16;
                margin-left: @vw106 + (@vw16 * 2);
                .innerImage {
                    .paddingRatio(228,310);
                }
            }
            .innerImage {
                height: 0;
                img, video {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                }
            }
        }
    }
}

@media all and (max-width: 1080px) {
    .bigHeaderBlock {
        .topCols {
            margin-bottom: @vw90-1080;
        }
        .bigMediaWrapper {
            padding: @vw100-1080 * 2.7 0 @vw100-1080 + @vw40-1080 0;
            .subTitle {
                margin-bottom: @vw30-1080;
            }
        }
        .extraText {
            margin-top: @vw100-1080 + @vw40-1080;
            padding-right: (@vw106-1080 * 2) + (@vw16-1080 * 2);
        }
        .images {
            .imageWrapper {
                &:first-child {
                    margin-bottom: @vw100-1080 + @vw80-1080;
                    width: (@vw106-1080 * 4) + (@vw16-1080 * 3);
                }
                &:last-child {
                    width: (@vw106-1080 * 2) + @vw16-1080;
                    margin-left: @vw106-1080 + (@vw16-1080 * 2);
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .bigHeaderBlock {
        .topCols {
            margin-bottom: @vw90-580;
            .col {
                width: 100% !important;
                display: block;
                &:first-child {
                    margin-bottom: @vw40-580;
                }
            }
        }
        .bigMediaWrapper {
            padding: @vw100-580 * 2 0 @vw100-580 + @vw40-580 0;
            .subTitle {
                margin-bottom: @vw30-580;
            }
        }
        .contentWrapper {
            .innerWrapper {
                padding-left: 0;
            }
        }
        .extraText {
            margin-top: @vw100-580 + @vw40-580;
            padding-right: 0;
        }
        .images {
            .imageWrapper {
                display: block !important;
                width: 100% !important;
                margin: 0 0 @vw30-580 0 !important;
                &:first-child {
                    margin-bottom: @vw30-580 !important;
                }
                &:last-child {
                    margin-left: 0 !important;
                }
            }
        }
    }
}