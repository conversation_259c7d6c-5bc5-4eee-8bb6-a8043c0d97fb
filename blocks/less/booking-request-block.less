// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.bookingRequestBlock {
  padding: @vw100 0;
  
  &.inview {
    .formHeader {
      .tinyTitle, .biggerTitle, .text {
        .transform(translateY(0));
        opacity: 1;
        transition: opacity 0.6s 0.2s ease-out, transform 0.6s 0.2s ease-out;
        .stagger(3, 0.1s);
      }
    }
    
    .progressIndicator {
      .transform(translateY(0));
      opacity: 1;
      transition: opacity 0.8s 0.4s ease-out, transform 0.8s 0.4s ease-out;
    }
    
    .formSteps {
      .transform(translateY(0));
      opacity: 1;
      transition: opacity 1s 0.6s ease-out, transform 1s 0.6s ease-out;
    }
  }
  
  .cols {
    margin-left: -@vw16;
    width: calc(100% ~"+" @vw32);
    .col {
      display: inline-block;
      margin: 0 @vw16;
      vertical-align: top;
      &:first-child {
        width: calc(33.3333% ~"-" @vw32);
        padding-right: @vw100;
      }
      &:last-child {
        width: calc(66.6666% ~"-" @vw32);
      }
    }
  }

  .formStep {
    .tinyTitle {
      margin-bottom: @vw15;
    }
  }
  
  .formHeader {
    margin-bottom: @vw12;
  }

  .arrowButton {
    background-color: @thirdColor;
    color: @primaryColor;
    border: none;
    height: @vw50;
    width: @vw50;
    line-height: @vw50;
    i {
      font-size: @vw16;
    }
  }
  
  .progressIndicator {
    margin-bottom: @vw60;
    .transform(translateY(@vw30));
    opacity: 0;
    
    .progressBar {
      width: 100%;
      height: 2px;
      background: rgba(255, 255, 255, 0.1);
      margin-bottom: @vw40;
      overflow: hidden;
      
      .progressFill {
        height: 100%;
        background: @thirdColor;
        width: 0%;
        transition: width 0.5s ease-out;
      }
    }
  }
  .stepIndicators {
      display: flex;
      justify-content: center;
      gap: @vw10;
      align-items: center;
      margin-top: @vw50;
      .stepIndicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        .transition(.3s);
        &.active {
          .stepNumber {
            background: @thirdColor;
            color: @primaryColor;
          }

          .stepLabel {
            color: @thirdColor;
          }
        }

        &.completed {
          .stepNumber {
            background: rgba(@thirdColor, .6);
            color: @primaryColor;
            
            // &:after {
            //   content: '✓';
            //   font-size: 12px;
            // }
          }
          
          .stepLabel {
            color: @thirdColor;
          }
        }
        
        .stepNumber {
          width: @vw50;
          height: @vw50;
          .rounded(50%);
          background: rgba(255, 255, 255, 0.1);
          color: @hardWhite;
          display: flex;
          line-height: @vw50;
          align-items: center;
          justify-content: center;
        }

        .stepLabel {
          font-size: @vw22;
          color: rgba(255, 255, 255, 0.7);
          text-align: center;
          transition: all 0.3s ease;
        }
      }
    }
  
  .formSteps {
    .transform(translateY(@vw30));
    opacity: 0;
    
    .formStep {
      display: none;
      
      &.active {
        display: block;
      }
      
      .stepTitle {
        font-size: 32px;
        margin-bottom: @vw15;
        color: @hardWhite;
      }
      
      .stepDescription {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: @vw40;
      }
    }
  }
  
  // Artist Selection Styles
  .artistSelection {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(32%, 1fr));
    gap: @vw8;
    margin-bottom: @vw40;
    
    .artistOption {
      .tinyTitle {
        margin-bottom: 0;
      }
      .artistLabel {
        cursor: pointer;
        display: block;
        
        input[type="checkbox"] {
          display: none;
        }
        
        .artistCard {
          background: rgba(255, 255, 255, 0.05);
          padding: @vw10;
          transition: all 0.3s ease;
          border: 1px solid transparent;
          position: relative;
          overflow: hidden;
          display: flex;
          flex-direction: row;
          justify-content: start;
          gap: @vw16;
          &:hover {
            background: rgba(255, 255, 255, 0.08);
            .transform(translateY(-5px));
          }
          
          .artistImage {
            width: @vw55;
            height: @vw55;
            overflow: hidden;
            margin-bottom: 0;
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.3s ease;
            }
          }
          
          .artistInfo {
            .artistName {
              font-weight: 600;
              color: @hardWhite;
              margin-bottom: @vw5;
            }
            
            .artistType {
              font-size: 14px;
              color: rgba(255, 255, 255, 0.6);
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }
          }
          
          .checkmark {
            position: absolute;
            top: @vw15;
            right: @vw15;
            width: @vw20;
            height: @vw20;
            .rounded(50%);
            background: @thirdColor;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            .transform(scale(0.5));
            transition: all 0.3s ease;

            &:after {
              content: '✓';
              color: @primaryColor;
              font-size: @vw12;
              font-weight: bold;
            }
          }
        }
      }
      
      &.selected {
        .artistCard {
          border-color: @thirdColor;
          background: rgba(255, 255, 255, 0.1);
          
          .checkmark {
            opacity: 1;
            .transform(scale(1));
          }
          
          .artistImage img {
            .transform(scale(1.05));
          }
        }
      }
    }
  }
  
  // Form Fields Styles
  .formFields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(47%, 1fr));
    gap: @vw25;
    
    .fieldGroup {
      &.full-width {
        grid-column: 1 / -1;
      }
      &.half-width {
        grid-column: span 1;
      }
      
      &.has-error {
        label {
          color: #ff6b6b;
        }
        
        input, select, textarea {
          border-color: #ff6b6b;
        }
      }
      &:has(.infoIcon:visible) {
        position: relative;
        input {
          padding-right: @vw60;
        }
      }
      .infoIcon {
        position: absolute;
        top: @vw40;
        right: @vw20;
        color: @thirdColor;
      }

      label {
        display: block;
        color: rgba(@thirdColor, .6);
        margin-bottom: @vw10;
        font-size: @vw16;
      }

      input, select, textarea {
        width: 100%;
        padding: @vw15 @vw20;
        // background: rgba(255, 255, 255, 0.05);
        background: transparent;
        // border: 2px solid rgba(255, 255, 255, 0.1);
        border: 1px solid transparent;
        border-bottom: 1px solid rgba(@thirdColor, .2);
        .rounded(0);
        color: @thirdColor;
        font-family: "Roboto", sans-serif;
        font-size: @vw20;
        .transition(.3s);
        &:focus {
          outline: none;
          border-color: @thirdColor;
          background: rgba(255, 255, 255, 0.08);
        }
        
        &.valid {
          border-color: #51cf66;
        }
        
        &.invalid {
          border-color: #ff6b6b;
        }
        
        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }
      }

      input[type="time"]::-webkit-calendar-picker-indicator{
        display: none;
      }
      input[type="date"]::-webkit-calendar-picker-indicator {
        display: none;
      }

      textarea {
        resize: none;
      }
      
      select {
        cursor: pointer;

        option {
          background: @primaryColor;
          color: @hardWhite;
        }
      }
    }
  }
  
  // Review Summary Styles
  .reviewSummary {
    .summarySection {
      background: rgba(255, 255, 255, 0.05);
      .rounded(@vw6);
      padding: @vw25;
      &:not(:last-child) {
        margin-bottom: @vw25;
      }
      > .tinyTitle {
        color: @thirdColor;
        margin-bottom: @vw15;
      }
      .review-row {
        color: rgba(@thirdColor, .6);
      }
    }
  }
  
  // Navigation Styles
  .formNavigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: @vw50;
  }
  
  // Validation Error Styles
  .validation-error {
    background: #ff6b6b;
    color: @hardWhite;
    padding: @vw15 @vw20;
    .rounded(@vw10);
    margin-bottom: @vw20;
    font-weight: 600;
    text-align: center;
  }

  // Selected Artists Counter
  .selected-artists-counter {
    text-align: center;
    color: @thirdColor;
    font-weight: 600;
    margin-top: @vw20;
    padding: @vw10 @vw20;
    background: rgba(255, 255, 255, 0.05);
    .rounded(@vw10);
    width: 100%;
    display: inline-block;
  }
  
  // Hidden Contact Form 7
  .hiddenForm {
    display: none;
  }

  // Success Message Styles
  .booking-success-message {
    text-align: center;
    padding: @vw60 @vw40;
    background: rgba(255, 255, 255, 0.05);
    .rounded(@vw12);
    margin: @vw40 0;

    .success-icon {
      font-size: 4rem;
      color: #28a745;
      margin-bottom: @vw20;
      font-weight: bold;
    }

    h3 {
      color: #28a745;
      margin-bottom: @vw16;
      font-size: 1.5rem;
    }

    p {
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: @vw30;
      font-size: 1.1rem;
    }

    .new-request-button {
      background: @thirdColor;
      color: @primaryColor;
      border: none;
      padding: @vw12 @vw24;
      .rounded(@vw6);
      font-size: 1rem;
      cursor: pointer;
      .transition(.3s);
      font-weight: 600;

      &:hover {
        background: lighten(@thirdColor, 10%);
        .transform(translateY(-2px));
      }
    }
  }

  // Loading States
  .submitButton {
    width: auto !important;
    padding: 0 @vw36;
    .rounded(@vw100);
    background: @hardWhite;
    color: @primaryColor;
    font-size: @vw18;
    &.loading {
      opacity: 0.7;
      cursor: not-allowed;
      position: relative;

      &:after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        margin: auto;
        border: 2px solid transparent;
        border-top-color: currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  @keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
  }

  // Step Indicator Disabled State
  .stepIndicator {
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed !important;
      pointer-events: none;
    }
  }

  .formStep.reviewStep {
    display: block !important;
    .reviewSummary {
    }
    .formNavigation {
      margin-top: @vw40;
      display: flex;
      justify-content: flex-end;
      gap: @vw20;
    }
  }
  .selectedArtistsList {
    .review-row {
      &:not(:last-child) {
        margin-bottom: @vw12;
      }
    }
    .artistCard {
      display: flex;
      gap: @vw10;
      .artistName {
        margin-bottom: 0;
      }
    }
    .artistImage {
      width: @vw55;
      height: @vw55;
      overflow: hidden;
      margin-bottom: 0;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
    }
  }
  .reviewSummary {
    .review-row {
      line-height: 1.4;
    }
  }

  // Information Panel Styling - Desktop
  .information {
    position: relative;
    z-index: 10;

    .infoText {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      background: @secondaryColorLight;
      padding: @vw50 @vw30;
      z-index: 100;
      max-width: 100%;
      .text {
        p {
          margin: 0;
          color: @primaryColor;
          strong {
            color: @primaryColor;
            display: flex;
            align-items: center;
            i {
              margin-right: @vw8;
              font-size: 20px;
              color: @primaryColor;
              width: @vw30;
              height: @vw30;
              .rounded(50%);
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
            }
          }
        }
      }

      // Arrow pointing to the info icon
      &::before {
        content: '';
        position: absolute;
        bottom: 50%;
        .transform(translateY(-50%) translateX(75%) rotate(-90deg));
        left: auto;
        right: 0;
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-top: 10px solid @secondaryColorLight;
      }
    }
  }
}

@media all and (max-width: 1080px) {
  .bookingRequestBlock {
    padding: @vw100-1080 0;

    .cols {
      margin-left: -@vw16-1080;
      width: calc(100% ~"+" @vw32-1080);
      .col {
        margin: 0 @vw16-1080;
        &:first-child {
          width: calc(33.3333% ~"-" @vw32-1080);
          padding-right: @vw100-1080;
        }
        &:last-child {
          width: calc(66.6666% ~"-" @vw32-1080);
        }
      }
    }

    .formStep {
      .tinyTitle {
        margin-bottom: @vw15-1080;
      }
    }

    .formHeader {
      margin-bottom: @vw12-1080;
    }

    .arrowButton {
      height: @vw50-1080;
      width: @vw50-1080;
      line-height: @vw50-1080;
      i {
        font-size: @vw16-1080;
      }
    }

    .progressIndicator {
      margin-bottom: @vw60-1080;
      .transform(translateY(@vw30-1080));

      .progressBar {
        margin-bottom: @vw40-1080;
      }
    }

    .stepIndicators {
      gap: @vw10-1080;
      margin-top: @vw50-1080;

      .stepIndicator {
        .stepNumber {
          width: @vw50-1080;
          height: @vw50-1080;
          line-height: @vw50-1080;
        }

        .stepLabel {
          font-size: @vw22-1080;
        }
      }
    }

    .formSteps {
      .transform(translateY(@vw30-1080));

      .formStep {
        .stepDescription {
          margin-bottom: @vw40-1080;
        }
      }
    }

    // Artist Selection Styles
    .artistSelection {
      grid-template-columns: repeat(auto-fill, minmax(48%, 1fr));
      gap: @vw8-1080;
      margin-bottom: @vw40-1080;

      .artistOption {
        .artistLabel {
          .artistCard {
            padding: @vw10-1080;
            gap: @vw16-1080;

            .artistImage {
              width: @vw55-1080;
              height: @vw55-1080;
            }

            .artistInfo {
              .artistName {
                margin-bottom: @vw5-1080;
              }
            }

            .checkmark {
              top: @vw15-1080;
              right: @vw15-1080;
              width: @vw20-1080;
              height: @vw20-1080;

              &:after {
                font-size: @vw12-1080;
              }
            }
          }
        }
      }
    }

    // Form Fields Styles
    .formFields {
      gap: @vw25-1080;

      .fieldGroup {
        &:has(.infoIcon:visible) {
          input {
            padding-right: @vw60-1080;
          }
        }
        .infoIcon {
          top: @vw40-1080;
          right: @vw20-1080;
        }

        label {
          margin-bottom: @vw10-1080;
          font-size: @vw16-1080;
        }

        input, select, textarea {
          padding: @vw15-1080 @vw20-1080;
          font-size: @vw20-1080;
        }
      }
    }

    // Review Summary Styles
    .reviewSummary {
      .summarySection {
        .rounded(@vw6-1080);
        padding: @vw25-1080;
        &:not(:last-child) {
          margin-bottom: @vw25-1080;
        }
        > .tinyTitle {
          margin-bottom: @vw15-1080;
        }
      }
    }

    // Navigation Styles
    .formNavigation {
      margin-top: @vw50-1080;
    }

    // Validation Error Styles
    .validation-error {
      padding: @vw15-1080 @vw20-1080;
      .rounded(@vw10-1080);
      margin-bottom: @vw20-1080;
    }

    // Selected Artists Counter
    .selected-artists-counter {
      margin-top: @vw20-1080;
      padding: @vw10-1080 @vw20-1080;
      .rounded(@vw10-1080);
    }

    // Success Message Styles
    .booking-success-message {
      padding: @vw60-1080 @vw40-1080;
      .rounded(@vw12-1080);
      margin: @vw40-1080 0;

      .success-icon {
        margin-bottom: @vw20-1080;
      }

      h3 {
        margin-bottom: @vw16-1080;
      }

      p {
        margin-bottom: @vw30-1080;
      }

      .new-request-button {
        padding: @vw12-1080 @vw24-1080;
        .rounded(@vw6-1080);
      }
    }

    // Loading States
    .submitButton {
      padding: 0 @vw36-1080;
      .rounded(@vw100-1080);
      font-size: @vw18-1080;
    }

    .formStep.reviewStep {
      .formNavigation {
        margin-top: @vw40-1080;
        gap: @vw20-1080;
      }
    }

    .selectedArtistsList {
      .review-row {
        &:not(:last-child) {
          margin-bottom: @vw12-1080;
        }
      }
      .artistCard {
        gap: @vw10-1080;
      }
      .artistImage {
        width: @vw55-1080;
        height: @vw55-1080;
      }
    }

    // Information Panel Styling - Tablet
    .information {
      .infoText {
        padding: @vw50-1080 @vw30-1080;
        .text {
          p {
            strong {
              i {
                margin-right: @vw8-1080;
                width: @vw30-1080;
                height: @vw30-1080;
              }
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .bookingRequestBlock {
    padding: @vw100-580 0;

    .cols {
      margin-left: 0;
      width: 100%;
      .col {
        display: block;
        width: 100% !important;
        margin: 0;
        &:first-child {
          padding-right: 0;
          margin-bottom: @vw50-580;
        }
      }
    }

    .formStep {
      .tinyTitle {
        margin-bottom: @vw15-580;
      }
    }

    .formHeader {
      margin-bottom: @vw12-580;
    }

    .arrowButton {
      height: @vw60-580;
      width: @vw60-580;
      line-height: @vw60-580;
      i {
        font-size: @vw18-580;
      }
    }

    .progressIndicator {
      margin-bottom: @vw60-580;
      .transform(translateY(@vw30-580));

      .progressBar {
        margin-bottom: @vw40-580;
      }
    }

    .stepIndicators {
      gap: @vw8-580;
      margin-top: @vw50-580;
      flex-wrap: wrap;

      .stepIndicator {
        .stepNumber {
          width: @vw56-580;
          height: @vw56-580;
          line-height: @vw56-580;
        }

        .stepLabel {
          font-size: @vw18-580;
        }
      }
    }

    .formSteps {
      .transform(translateY(@vw30-580));

      .formStep {
        .stepDescription {
          margin-bottom: @vw40-580;
        }
      }
    }

    // Artist Selection Styles
    .artistSelection {
      grid-template-columns: 1fr;
      gap: @vw12-580;
      margin-bottom: @vw40-580;

      .artistOption {
        .artistLabel {
          .artistCard {
            padding: @vw15-580;
            gap: @vw16-580;

            .artistImage {
              width: @vw60-580;
              height: @vw60-580;
            }

            .artistInfo {
              .artistName {
                margin-bottom: @vw5-580;
              }
            }

            .checkmark {
              top: @vw15-580;
              right: @vw15-580;
              width: @vw24-580;
              height: @vw24-580;

              &:after {
                font-size: @vw14-580;
              }
            }
          }
        }
      }
    }

    // Form Fields Styles
    .formFields {
      display: block;
      .fieldGroup {
        display: block;
        width: 100%;
        &:not(:last-child) {
          margin-bottom: @vw20-580;
        }
        &.full-width, &.half-width {
          grid-column: 1;
        }

        &:has(.infoIcon:visible) {
          input {
            padding-right: @vw60-580;
          }
        }
        .infoIcon {
          top: @vw40-580;
          right: @vw20-580;
        }

        label {
          margin-bottom: @vw10-580;
          font-size: @vw16-580;
        }

        input, select, textarea {
          padding: @vw22-580 @vw20-580;
          font-size: @vw22-580;
        }
      }
    }

    // Review Summary Styles
    .reviewSummary {
      .summarySection {
        .rounded(@vw6-580);
        padding: @vw20-580;
        &:not(:last-child) {
          margin-bottom: @vw20-580;
        }
        > .tinyTitle {
          margin-bottom: @vw15-580;
        }
      }
    }

    // Navigation Styles
    .formNavigation {
      margin-top: @vw40-580;
      flex-direction: column;
      gap: @vw15-580;
    }

    // Validation Error Styles
    .validation-error {
      padding: @vw15-580 @vw20-580;
      .rounded(@vw10-580);
      margin-bottom: @vw20-580;
    }

    // Selected Artists Counter
    .selected-artists-counter {
      margin-top: @vw20-580;
      padding: @vw12-580 @vw20-580;
      .rounded(@vw10-580);
    }

    // Success Message Styles
    .booking-success-message {
      padding: @vw50-580 @vw30-580;
      .rounded(@vw12-580);
      margin: @vw30-580 0;

      .success-icon {
        margin-bottom: @vw20-580;
      }

      h3 {
        margin-bottom: @vw16-580;
      }

      p {
        margin-bottom: @vw25-580;
      }

      .new-request-button {
        padding: @vw15-580 @vw30-580;
        .rounded(@vw6-580);
      }
    }

    // Loading States
    .submitButton {
      padding: 0 @vw30-580;
      .rounded(@vw100-580);
      font-size: @vw18-580;
    }

    .formStep.reviewStep {
      .formNavigation {
        margin-top: @vw30-580;
        gap: @vw15-580;
      }
    }

    .selectedArtistsList {
      .review-row {
        &:not(:last-child) {
          margin-bottom: @vw12-580;
        }
      }
      .artistCard {
        gap: @vw12-580;
      }
      .artistImage {
        width: @vw60-580;
        height: @vw60-580;
      }
    }

    // Information Panel Styling - Mobile
    .information {
      .infoText {
        padding: @vw40-580 @vw25-580;
        .text {
          p {
            strong {
              i {
                margin-right: @vw8-580;
                width: @vw30-580;
                height: @vw30-580;
              }
            }
          }
        }

        // Adjust arrow for mobile
        &::before {
          .transform(translateY(-50%) translateX(50%) rotate(-90deg));
        }
      }
    }
  }
}