//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';
.artistTwoColumnTextBlock {
  &.inview {
    p {
      opacity: 1;
      .transform(translateY(0));
      transition: opacity 0.6s 0.2s ease-out, transform 0.6s 0.2s ease-out;
      .stagger(90, 0.15s);
    }
  }
  p {
    opacity: 0;
    .transform(translateY(@vw30));
  }
  a {
    color: @hardWhite;
    text-decoration: underline;
  }
  .cols {
    width: calc(100% ~"+" @vw16);
    margin-left: -@vw8;
    .col {
      display: inline-block;
      margin: 0 @vw8;
      width: calc(50% ~"-" @vw16);
      vertical-align: top;
      &:not(:first-child) {
        opacity: .7;
      }
    }
  }
  p {
    &:not(:last-child) {
      margin-bottom: @vw22;
    }
  }
}

@media all and (max-width: 1080px) {
  .artistTwoColumnTextBlock {
    p {
      .transform(translateY(@vw30-1080));
    }
    .cols {
      width: calc(100% ~"+" @vw16-1080);
      margin-left: -@vw8-1080;
      .col {
        margin: 0 @vw8-1080;
        width: calc(50% ~"-" @vw16-1080);
      }
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-1080;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .artistTwoColumnTextBlock {
    p {
      .transform(translateY(@vw30-580));
    }
    .cols {
      width: 100%;
      margin-left: 0;
      .col {
        margin: 0;
        width: 100%;
        display: block;
        &:not(:first-child) {
          margin-top: @vw30-580;
          opacity: 1;
        }
      }
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-580;
      }
    }
  }
}