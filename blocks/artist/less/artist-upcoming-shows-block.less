//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';

.artistUpcomingShowsBlock {
  &.inview {
    .upcomingShowsTitle {
      .transform(translateY(0));
      opacity: 1;
      transition: opacity 0.6s 0.2s ease-out, transform 0.6s 0.2s ease-out;
    }
    .bandsintownWidget {
      .bit-widget {
        .bit-event {
          opacity: 1;
          .transform(translateY(0));
          -webkit-transition: opacity 0.45s ease-out, transform 0.45s ease-out;
          transition: opacity 0.45s ease-out, transform 0.45s ease-out;
          .stagger(100, 0.1s, 0.4s);
        }
      }
    }

    .showItem {
      .transform(translateY(0));
      opacity: 1;
      transition: opacity 0.8s ease-out, transform 0.8s ease-out;
      .stagger(10, 0.1s, 0.4s);
    }
  }

  .bandsintownSimple {
    max-width: 100%;
  }

  .bit-widget .bit-event .bit-offers-text {
      white-space: normal !important;
    }

  .bandsintownWidget {
    max-width: 100%;
    margin: 0 auto;
    .bit-widget-initializer {
      display: block;
      width: 100%;
    }
    .bit-play-my-city-button {
      display: none !important;
    }

    // Hide default widget styling and apply custom
    .bit-widget {
      font-family: 'ApexMk2-Regular', 'ApexMk2-Regular', Arial, sans-serif !important;
      background: transparent !important;

      // Hide the track button and logo
      .bit-top-track-button,
      .bit-nav-bar-container,
      .bit-logo-container {
        display: none !important;
      }

      // Style the events container
      .bit-events-container {
        background: transparent !important;
        padding: 0 !important;
      }

      .bit-upcoming-events {
        margin: 0 !important;
        background: transparent !important;
        padding: 0 !important;
      }

      

      // Style individual events to match your design
      .bit-event {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        background: transparent !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        margin-bottom: 0 !important;
        padding: @vw17 0 !important;
        opacity: 0;
        line-height: 1 !important;
        .transform(translateY(@vw30));
        // Left side: Date and venue info
        a {
          text-decoration: none !important;
        }
        .bit-details:first-child {
          display: inline-block !important;
          width: 80% !important;
          div {
            &:first-child {
              width: 25% !important
            }
            display: inline-block;
            width: 36% !important;
            padding-right: @vw40;
            vertical-align: middle;
          }
          .bit-date {
            font-family: 'ApexMk2-Regular', Arial, sans-serif !important;
            font-weight: bold !important;
            font-size: @vw20 !important;
            color: @hardWhite !important;
            min-width: 120px !important;
            text-transform: uppercase !important;
          }

          .bit-titleWrapper {
            flex: 1 !important;

            .bit-venue {
              width: 100% !important;
              font-family: 'ApexMk2-Regular', Arial, sans-serif !important;
              font-size: @vw20 !important;
              color: rgba(255, 255, 255, 0.8) !important;
              text-transform: uppercase !important;
              letter-spacing: 0.5px !important;
            }
          }

          .bit-location {
            font-family: 'ApexMk2-Regular', Arial, sans-serif !important;
            font-weight: bold !important;
            font-size: @vw20 !important;
            color: @secondaryColor !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
            text-align: left !important;
          }
        }

        // Right side: Buttons
        .bit-event-buttons {
          display: flex !important;
          gap: @vw10 !important;

          .bit-rsvp-container {
            display: none !important;
          }
          .bit-offers-container {
            .bit-button {
              background: @secondaryColor !important;
              color: @hardWhite !important;
              padding: @vw11 @vw30 !important;
              .rounded(@vw6);
              margin-left: 0 !important;
              font-family: 'ApexMk2-Regular', Arial, sans-serif !important;
              font-size: @vw20 !important;
              font-weight: bold !important;
              letter-spacing: 1px !important;
              text-transform: uppercase !important;
              .transitionMore(background, .3s);
              border: none !important;
              text-decoration: none !important;
              line-height: 1 !important;
              width: @vw100 * 2 !important;
              &:hover {
                background: @secondaryColorLight !important;
              }
            }
          }
        }
      }

      // Hide show all button or style it
      .bit-upcoming-events-show-all-button {
        display: none !important;
      }

      // Hide iframe
      .google-pixel-iframe {
        display: none !important;
      }
    }
  }

}

@media all and (max-width: 1080px) {
  .artistUpcomingShowsBlock {
    .upcomingShowsTitle {
      .transform(translateY(@vw30-1080));
    }
    .bandsintownWidget {
      .bit-widget {
        .bit-event {
          padding: @vw17-1080 0 !important;
          .transform(translateY(@vw30-1080));
          .bit-details:first-child {
            div {
              padding-right: @vw40-1080;
            }
            .bit-date {
              font-size: @vw20-1080 !important;
            }
            .bit-titleWrapper {
              .bit-venue {
                font-size: @vw20-1080 !important;
              }
            }
            .bit-location {
              font-size: @vw20-1080 !important;
            }
          }
          .bit-event-buttons {
            gap: @vw10-1080 !important;
            .bit-offers-container {
              .bit-button {
                padding: @vw11-1080 @vw30-1080 !important;
                .rounded(@vw6-1080);
                font-size: @vw20-1080 !important;
                width: @vw100-1080 * 2 !important;
              }
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .artistUpcomingShowsBlock {
    .upcomingShowsTitle {
      .transform(translateY(@vw30-580));
    }
    .bandsintownWidget {
      .bit-widget {
        .bit-event {
          padding: @vw17-580 0 !important;
          .transform(translateY(@vw30-580));
          flex-direction: column !important;
          align-items: flex-start !important;
          .bit-details:first-child {
            width: 100% !important;
            margin-bottom: @vw20-580;
            div {
              width: 100% !important;
              display: block !important;
              padding-right: 0;
              margin-bottom: @vw10-580;
              &:first-child {
                width: 100% !important;
              }
            }
            .bit-date {
              font-size: @vw20-580 !important;
            }
            .bit-titleWrapper {
              .bit-venue {
                font-size: @vw20-580 !important;
              }
            }
            .bit-location {
              font-size: @vw20-580 !important;
            }
          }
          .bit-event-buttons {
            width: 100%;
            margin-left: 0 !important;
            gap: @vw10-580 !important;
            .bit-offers-container {
              width: 100%;
              .bit-button {
                padding: @vw11-580 @vw30-580 !important;
                .rounded(@vw6-580);
                font-size: @vw20-580 !important;
                width: 100%;
                text-align: center;
                width: 100% !important;
              }
            }
          }
        }
      }
    }
  }
}
