<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$images = get_field('images', $artist_id);
if ($images): ?>
    <section class="artistBigSliderBlock" data-show-cursor>
        <div class="contentWrapper smaller">
            <div class="sliderWrapper">
                <div class="slider" data-slider data-loop-slider="true">
                    <?php foreach ($images as $img): ?>
                        <div class="slide">
                            <div class="imageWrapper">
                                <div class="innerImage">
                                    <img class="lazy" data-src="<?= esc_url($img['sizes']['large']) ?>" alt="<?= esc_attr($name) ?>" />
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php if (count($images) > 1): ?>
                <div class="sliderButton arrowButton prev" data-prev><i class="icon-chevron-left"></i><i class="icon-chevron-left"></i></div>
                <div class="sliderButton arrowButton next" data-next><i class="icon-chevron-right"></i><i class="icon-chevron-right"></i></div>
                <?php endif; ?>
            </div>
        </div>
    </section>
<?php endif; ?>
