<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$logo = get_field('logo', $artist_id);
$category = get_field('category', $artist_id);
$name = get_the_title($artist_id);
$socials = [
    'instagram' => get_field('instagram', $artist_id),
    'tiktok' => get_field('tiktok', $artist_id),
    'youtube' => get_field('youtube', $artist_id),
    'spotify' => get_field('spotify', $artist_id),
    'soundcloud' => get_field('soundcloud', $artist_id),
    'facebook' => get_field('facebook', $artist_id),
    'twitter_x' => get_field('twitter_x', $artist_id),
    'hardstyle' => get_field('hardstyle', $artist_id),
];
$image = get_field('image', $artist_id);
$presskit = get_field('presskit', $artist_id);
$visual_pack = get_field('visual_pack', $artist_id);

// Global passwords from customizer
$presskit_password = get_theme_mod('customTheme-main-callout-presskit-password');
$visual_pack_password = get_theme_mod('customTheme-main-callout-visual-pack-password');
$artist_managers = get_field('artist_managers', $artist_id);
?>
<section class="artistHeaderBlock" data-init data-show-cursor>
    <? if ($image): ?>
        <div class="backgroundImage">
            <img src="<?= esc_url($image['url']) ?>" alt="<?= esc_attr($name) ?>" />
        </div>
    <?php endif; ?>
    <div class="contentWrapper smaller">
        <?php if ($logo): ?>
            <img class="artistLogo" src="<?= esc_url($logo['url']) ?>" alt="Logo <?= esc_attr($name) ?>" />
        <?php endif; ?>
        <?php if ($category): ?>
            <div class="ArtistGenre"><?= esc_html($category) ?></div>
        <?php endif; ?>
        <h1 class="hugeTitle" data-lines data-words data-parallax data-parallax-speed="-1"><?= esc_html($name) ?></h1>
        <div class="socials">
            <?php foreach ($socials as $key => $url):
                if ($url): ?>
                    <a href="<?= esc_url($url) ?>" target="_blank" rel="noopener" class="social -<?= esc_attr($key) ?>">
                        <i class="icon-<?= esc_attr($key) ?>"></i>
                    </a>
                <?php endif;
            endforeach; ?>
        </div>
        <a href="<?= esc_url(get_post_type_archive_link('artist')) ?>" class="textLink backToArtists">
            <span class="arrows"><i class="icon-chevron-left"></i><i class="icon-chevron-left"></i></span>
            <span class="innerText">&nbsp;All artists</span>
        </a>
        <div class="infoWrapper">
            <?php if ($image): ?>
                <div class="imageWrapper">
                    <div class="innerImage">
                        <img data-parallax data-parallax-speed="2" src="<?= esc_url($image['url']) ?>" alt="<?= esc_attr($name) ?>" />
                    </div>
                </div>
            <?php endif; ?>
             <div class="contactDetails">
                <h2 class="normalTitle">Contact</h2>
                <div class="text">
                    <p>
                    <?php echo get_theme_mod('customTheme-main-artist-contact') ?>
                    </p>
                </div>

                <!-- Booking Request Button -->
                <a class="button" title="Book <?= esc_html($name) ?>" href="<?= esc_url(home_url('/booking-request?artist_id=' . $artist_id)) ?>">
                    <span class="innerText">Book <?= esc_html($name) ?></span>
                    <span class="arrows">
                        <i class="icon-arrow-right-up"></i>
                        <i class="icon-arrow-right-up"></i>
                    </span>
                </a>

                <?php if ($artist_managers):
                    foreach ((array)$artist_managers as $manager_id):
                        $email = get_field('email', $manager_id);
                        if ($email): ?>
                        <!-- go to detail page of team member -->
                            <a class="button" href="<?= esc_url(get_permalink($manager_id)) ?>" title="Contact <?= get_the_title($manager_id) ?>">
                                <!-- <span class="innerText">Mail <?= get_the_title($manager_id) ?></span> -->
                                <span class="innerText">Contact Management</span>
                                <span class="arrows">
                                    <i class="icon-arrow-right-up"></i>
                                    <i class="icon-arrow-right-up"></i>
                                </span>
                            </a>
                        <?php endif;
                    endforeach;
                endif; ?>
                <?php if ($presskit): ?>
                    <div class="button password-protected-download"
                            data-url="<?= esc_url($presskit) ?>"
                            data-password="<?= esc_attr($presskit_password) ?>"
                            data-type="presskit"
                            title="Download Presskit">
                        <span class="innerText">
                            Download Presskit
                        </span>
                        <span class="arrows">
                            <i class="icon-arrow-right-up"></i>
                            <i class="icon-arrow-right-up"></i>
                        </span>
                    </div>
                <?php endif; ?>
                <?php if ($visual_pack): ?>
                    <div class="button password-protected-download"
                            data-url="<?= esc_url($visual_pack) ?>"
                            data-password="<?= esc_attr($visual_pack_password) ?>"
                            data-type="visual_pack"
                            title="Download Visual Pack">
                        <span class="innerText">
                            Download Visual Pack
                        </span>
                        <span class="arrows">
                            <i class="icon-arrow-right-up"></i>
                            <i class="icon-arrow-right-up"></i>
                        </span>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Password Protection Modal -->
<div id="passwordModal" class="password-modal" style="display: none;">
    <div class="password-modal-overlay"></div>
    <div class="password-modal-content">
        <div class="password-modal-header">
            <h3 class="normalTitle" id="passwordModalTitle">PRESSKIT — PASSWORD</h3>
        </div>
        <div class="password-modal-body">
            <p>The content is password protected. To view it, please enter the password below:</p>
            <div class="password-input-container">
                <input type="password" id="passwordInput" placeholder="ENTER PASSWORD" autocomplete="off">
            </div>
            <div class="password-modal-buttons">
                <button type="button" class="button-secondary" id="passwordModalClose">CLOSE</button>
                <button type="button" class="button-primary" id="passwordModalApply">APPLY</button>
            </div>
            <div id="passwordError" class="password-error" style="display: none;">
                Incorrect password. Please try again.
            </div>
        </div>
    </div>
</div>
