<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$spotify_id = get_field('spotify_id', $artist_id);
$spotify_token = apply_filters('artist_spotify_token', ''); // Zorg dat je een geldige token krijgt via filter of functie
if ($spotify_id && $spotify_token):
    $release = get_artist_spotify_latest_release($spotify_id, $spotify_token);
    if ($release): ?>
        <section class="artistLatestReleasblock" data-init data-show-cursor>
            <div class="contentWrapper smaller">
                <div class="cols">
                    <div class="col">
                        <?php if (!empty($release['images'][0]['url'])): ?>
                            <img class="lazy" data-src="<?= esc_url($release['images'][0]['url']) ?>" alt="<?= esc_attr($release['name']) ?>" />
                        <?php endif; ?>
                    </div>
                    <div class="col">
                        <div class="ReleaseInfo">
                            <h3 class="smallTitle">Latest Release</h3>
                            <div class="text">
                                <?php foreach ($release['artists'] as $artist): ?>
                                    <?= esc_html($artist['name']) ?>
                                    <?php if ($artist !== end($release['artists'])): ?>& <?php endif; ?>
                                <?php endforeach; ?> - <?= esc_html($release['name']) ?>
                            </div>
                            <a class="button" href="<?= esc_url($release['external_urls']['spotify']) ?>" target="_blank">
                                <span class="innerText">Listen on Spotify</span>
                                <span class="arrows">
                                    <i class="icon-arrow-right-up"></i>
                                    <i class="icon-arrow-right-up"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif;
endif;
