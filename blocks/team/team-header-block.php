<?php
$team_id = $args['team_id'] ?? get_the_ID();
$name = get_the_title($team_id);
$photo = get_field('photo', $team_id);
$category = get_field('category', $team_id);
$phone = get_field('phone', $team_id);
$email = get_field('email', $team_id);
$whatsapp = get_field('whatsapp', $team_id);
$instagram = get_field('instagram', $team_id);
$tiktok = get_field('tiktok', $team_id);
$facebook = get_field('facebook', $team_id);
$linkedin = get_field('linkedin', $team_id);
$website = get_field('website', $team_id);
?>
<section class="teamHeaderBlock noMarginBottom" data-init data-show-cursor>
    <div class="contentWrapper smaller">
        <div class="innerWrapper">
            <a href="<?= esc_url(home_url('/team')) ?>" class="textLink backToTeam">
                <span class="arrows"><i class="icon-chevron-left"></i><i class="icon-chevron-left"></i></span>
                <span class="innerText">&nbsp;Back to team overview</span>
            </a>
            <?php if ($photo): ?>
                <div class="memberPhotoWrapper">
                    <div class="memberPhoto">
                        <div class="pulse pulse-1"></div>
                        <div class="pulse pulse-2"></div>
                        <div class="pulse pulse-3"></div>
                        <img src="<?= esc_url($photo['sizes']['thumbnail']) ?>" alt="<?= esc_attr($name) ?>" />
                    </div>
                </div>
            <?php endif; ?>

            <!-- Category/Function -->
            <?php if ($category): ?>
                <div class="normalTitle white" data-lines data-words><?= esc_html($category) ?></div>
            <?php endif; ?>

            <!-- Name -->
            <h1 class="hugeTitle white" data-lines data-words><?= esc_html($name) ?></h1>

            <!-- Related Artists Marquee -->
        <?php
        // Zoek alle artiesten die dit team member als manager hebben
        $related_artists = new WP_Query([
            'post_type' => 'artist',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'meta_query' => [
                [
                    'key' => 'artist_managers',
                    'value' => '"' . $team_id . '"',
                    'compare' => 'LIKE'
                ],
                [
                    'key' => 'image',
                    'compare' => 'EXISTS',
                ]
            ],
        ]);

        if ($related_artists->have_posts()): ?>
            <div class="relatedArtistsWrapper">
                <div class="marqueeWrapper" data-init>
                    <div class="marquee">
                        <div class="marqueeScroll">
                            <div class="itemsContainer">
                                <?php while ($related_artists->have_posts()): $related_artists->the_post();
                                    $artist_name = get_the_title();
                                    $artist_category = get_field('category');
                                    $artist_link = get_permalink();
                                    $artist_image = get_field('image', get_the_ID());
                                    $artist_id = get_the_ID();
                                ?>
                                <a class="artistMarqueeItem item" title="<? esc_html($artist_name) ?>" href="<? echo esc_url($artist_link); ?>">
                                    <span class="artistMarqueeImage">
                                        <img class="lazy" data-src="<?php echo esc_url($artist_image['sizes']['thumbnail']); ?>" alt="<?php echo esc_attr($artist_image['alt'] ?? $artist_name); ?>" />
                                    </span>
                                    <span class="artistMarqueeInfo">
                                        <span class="tinyTitle"><?php echo esc_html($artist_name); ?></br><span class="white">Book&nbsp;<?php echo esc_html($artist_name); ?>&nbsp;now</span></span>
                                        <span class="arrows"><i class="icon-arrow-right-up"></i><i class="icon-arrow-right-up"></i></span>
                                    </span>
                                </a>
                                <?php endwhile; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php wp_reset_postdata(); ?>
        <?php endif; ?>

        <!-- Contact details -->
            <div class="contactDetails">
                <?php if ($phone): ?>
                    <a href="tel:<?= esc_attr($phone) ?>" class="button">
                        <span class="innerText"><?= esc_html($phone) ?></span>
                        <span class="arrows"><i class="icon-phone"></i><i class="icon-phone"></i></span>
                    </a>
                <?php endif; ?>

                <?php if ($email): ?>
                    <a href="mailto:<?= antispambot($email) ?>" class="button">
                        <span class="innerText"><?= esc_html($email) ?></span>
                        <span class="arrows"><i class="icon-mail"></i><i class="icon-mail"></i></span>
                    </a>
                <?php endif; ?>

                <?php if ($whatsapp): ?>
                    <a href="<?= esc_url($whatsapp) ?>" target="_blank" rel="noopener" class="button">
                        <span class="innerText">Send a WhatsApp message to <?= esc_html(explode(' ', $name)[0]) ?></span>
                        <span class="arrows"><i class="icon-whatsapp"></i><i class="icon-whatsapp"></i></span>
                    </a>
                <?php endif; ?>

                <?php if ($instagram): ?>
                    <a href="<?= esc_url($instagram) ?>" target="_blank" rel="noopener" class="button">
                        <span class="innerText"><?= esc_html(str_replace(['https://instagram.com/', 'https://www.instagram.com/', 'https://instagram.com/@', 'https://www.instagram.com/@', '@'], '', $instagram)) ?></span>
                        <span class="arrows"><i class="icon-instagram"></i><i class="icon-instagram"></i></span>
                    </a>
                <?php endif; ?>

                <?php if ($tiktok): ?>
                    <a href="<?= esc_url($tiktok) ?>" target="_blank" rel="noopener" class="button">
                        <span class="innerText"><?= esc_html(str_replace(['https://tiktok.com/', 'https://www.tiktok.com/', 'https://tiktok.com/@', 'https://www.tiktok.com/@', '@'], '', $tiktok)) ?></span>
                        <span class="arrows"><i class="icon-tiktok"></i><i class="icon-tiktok"></i></span>
                    </a>
                <?php endif; ?>

                <?php if ($website): ?>
                    <a href="<?= esc_url($website) ?>" target="_blank" rel="noopener" class="button">
                        <span class="innerText"><?= esc_html(str_replace(['https://', 'http://'], '', $website)) ?></span>
                        <span class="arrows"><i class="icon-globe"></i><i class="icon-globe"></i></span>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
