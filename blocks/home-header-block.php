<?php
$size = 'full'; 
$subtitle = get_field('subtitle');
$buttons = get_field('buttons');
$quick_artists_title = get_field('quick_artists_title');
$artists_amount = get_field('artists_amount');
$hide_quick_links = get_field('hide_quick_links');
$video = get_field('background_video');
$image = get_field('background_image');
$bottom_image = get_field('bottom_image');
?>
<section class="homeHeaderBlock dark" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
   <div class="backgroundVideo" data-parallax data-parallax-direct="true" data-parallax-speed="-1">
      <?php if ($video): ?>
          <video class="video" muted playsinline loop autoplay>
              <source src="<?php echo esc_url($video); ?>" type="video/mp4">
          </video>
      <?php elseif ($image): ?>
          <img src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
        <?php endif; ?>
    </div>
  <span class="backgroundWrapper"><span class="background"data-parallax data-parallax-speed="4"></span></span>
  <svg viewBox="0 0 551.11 412.45">
    <path d="M68.04,294.26c1.29-2.34,2.34-4.35,3.48-6.32C125.77,193.96,180.06,100,234.18,5.94,236.75,1.47,239.52-.08,244.65,0c19.82.34,39.64.14,60.6.14-45.54,78.89-90.69,157.12-136.31,236.15h6.75c89.65,0,179.29.03,268.94-.1,4.04,0,6.2,1.26,8.15,4.73,9.05,16.11,18.38,32.05,27.59,48.06.89,1.55,1.7,3.14,2.85,5.27H68.04Z"/>
    <path d="M427.49,197.36c-22.34,0-43.65.05-64.95-.12-1.41-.01-3.38-1.37-4.13-2.66-19.73-33.94-39.34-67.94-58.88-101.99-.68-1.19-.98-3.33-.37-4.41,10.59-18.65,21.35-37.2,32.58-56.64,31.97,55.37,63.59,110.13,95.75,165.81Z"/>
    <path d="M111.98,412.36c3.35-5.85,6.22-10.86,9.11-15.87,11.39-19.75,22.74-39.52,34.28-59.18.99-1.68,3.29-3.65,5-3.67,21.15-.25,42.3-.16,64.06-.16-.65,1.56-1.01,2.77-1.63,3.83-13.8,23.93-27.59,47.86-41.52,71.72-.84,1.45-2.8,3.18-4.25,3.19-21.31.21-42.63.14-65.05.14Z"/>
    <path d="M0,412.06c3.94-6.89,7.61-13.36,11.32-19.79,10.48-18.16,21.06-36.25,31.39-54.5,1.78-3.15,3.66-4.42,7.34-4.39,20.48.18,40.96.09,62.42.09-1.03,2.09-1.71,3.66-2.56,5.13-13.46,23.35-26.96,46.68-40.4,70.05-1.3,2.26-2.48,3.83-5.63,3.8-20.32-.18-40.64-.09-60.96-.1-.78,0-1.55-.15-2.92-.29Z"/>
    <path d="M326.95,333.48c22.04,0,43.35-.04,64.65.1,1.31,0,3.15,1.2,3.83,2.37,14.4,24.73,28.67,49.54,42.95,74.34.23.4.26.9.51,1.8-1.43.1-2.67.27-3.91.27-19.66.02-39.31.09-58.96-.11-1.88-.02-4.59-1.3-5.49-2.83-14.32-24.39-28.41-48.92-42.54-73.42-.31-.54-.48-1.16-1.03-2.51Z"/>
    <path d="M551.11,412.36c-9.69,0-18.82,0-27.95,0-11.83,0-23.66.11-35.48-.12-1.75-.03-4.25-1.24-5.09-2.67-14.46-24.7-28.71-49.51-43-74.31-.16-.27-.16-.63-.33-1.38,1.27-.14,2.49-.39,3.71-.39,19.66-.02,39.31-.09,58.97.1,1.92.02,4.72,1.16,5.6,2.67,14.4,24.54,28.55,49.22,42.75,73.87.24.41.34.9.82,2.23Z"/>
    <path d="M276.16,127.69c13.21,23.51,25.96,46.17,39,69.37h-78.79c13.25-23.11,26.25-45.76,39.79-69.37Z"/>
  </svg>
  <div class="artistSlider">
    <div class="imageSlider">
    <?php 
     $artists = new WP_Query([
          'post_type' => 'artist',
          'post_status'    => 'publish',
          'posts_per_page' => -1,
          'orderby' => 'rand',
          'meta_query' => [
            [
              'key' => 'slider_image_home',
              'compare' => 'EXISTS',
            ],
            [
              'key' => 'roster_type',
              'value' => 'main',
              'compare' => '=',
            ],
          ],
        ]);
      $projectImages = array();
      if ( $artists->have_posts() ) :
        foreach ( $artists->posts as $post ) :
          setup_postdata($post);
          $title = get_the_title($post->ID);
          $projectImage = get_field('slider_image_home', $post->ID);
          if (is_array($projectImage) && isset($projectImage['ID'])) {
      ?>
      <div class="slide">
        <div class="imageSlide"><img src="<?php echo esc_url($projectImage['sizes']['large']); ?>" alt="<?php echo esc_attr($projectImage['alt'] ?? $title); ?>"></div>
      </div>
      <?php 
          }
        endforeach;
        wp_reset_postdata();
      endif; 
    ?>
    </div>
  </div>
  <div class="titleWrapper">
    <div class="contentWrapper">
      <div class="innerCol small">
        <div class="sliderIndicatorWrapper">
          <span class="sliderIndicatorCount"><span class="currentSlide">1</span>/<span class="totalSlides">1</span></span>
          <div class="sliderIndicatorBar"><div class="sliderIndicatorProgress"></div></div>
        </div>
      <div class="titleSlider">
        
      <?php $projectImages = array();
      if ( $artists->have_posts() ) :
        foreach ( $artists->posts as $post ) :
          setup_postdata($post);
          $title = get_the_title($post->ID);
          $projectImage = get_field('slider_image_home', $post->ID);
          $projectLink = get_permalink($post->ID);
          if (is_array($projectImage) && isset($projectImage['ID'])) {
      ?>
        <div class="titleSlide">
          <span class="inlineCol"><h3 class="tinyTitle"><?php echo esc_html($title); ?></h3></span>
          <a href="<?php echo esc_url($projectLink); ?>" class="textLink">
            <span class="innerText"><?php the_field("more_info") ?></span>
            <span class="arrows"><i class="icon-arrow-right-up"></i><i class="icon-arrow-right-up"></i></span>
          </a>
        </div>
      <?php 
        }
      endforeach;
      wp_reset_postdata();
    endif; ?>
      </div>
      <div class="navigation">
        <div class="arrowButton prev">
            <i class="icon-chevron-left"></i>
            <i class="icon-chevron-left"></i>
          </div>
          <div class="arrowButton next">
            <i class="icon-chevron-right"></i>
            <i class="icon-chevron-right"></i>
          </div>
      </div>
    </div>
      <div class="innerCol" data-parallax data-parallax-speed="2">
         <?php if ($subtitle): ?>
          <div class="smallTitle white" data-lines data-words><?php echo esc_html($subtitle); ?></div>
          <?php endif; ?>
        <?php $title = get_field('title'); if ($title): ?>
          <h1 class="bigTitle white" data-lines data-words><?php echo esc_html($title); ?></h1>
        <?php endif; ?>
        <!-- <div class="buttonWrapper">
          <?php if ($buttons): ?>
            <?php foreach ($buttons as $button): ?>
              <?php if (!empty($button['button'])): ?>
                <?php render_button_from_array($button['button']); ?>
              <?php endif; ?>
            <?php endforeach; ?>
          <?php endif; ?>
        </div> -->
      </div>
    </div>
  </div>
  <?php if (!$hide_quick_links): ?>
  <div class="quickArtistsWrapper">
      <div class="bottomImageWrapper">
        <img class="bottomImage" src="<?php echo esc_url($bottom_image['sizes']['large']); ?>" alt="<?php echo esc_attr($bottom_image['alt']); ?>" />
      </div>
      <!-- <?php if ($quick_artists_title): ?>
        <h2 class="tinyTitle white" data-lines data-words><i class="icon-flame"></i>&nbsp; <?php echo esc_html($quick_artists_title); ?>&nbsp; <i class="icon-flame"></i></h2>
      <?php endif; ?> -->
      <?php
      $artists_query = new WP_Query([
        'post_type' => 'artist',
        'post_status'    => 'publish',
        'posts_per_page' => $artists_amount ? intval($artists_amount) : 5,
        'orderby' => 'rand',
        'meta_query' => [
          [
            'key' => 'image',
            'compare' => 'EXISTS',
          ],
        ],
      ]);
      if ($artists_query->have_posts()): ?>
        <div class="marqueeWrapper" data-init>
          <div class="marquee" data-marquee data-marquee-direction="right" data-marquee-speed="25" data-marquee-scroll-speed="5" data-marquee-swipe="true">
              <div class="marqueeScroll">
                <?php
                // Verzamel alle artiesten opnieuw voor de marquee (random, met image)
                $marquee_artists = new WP_Query([
                  'post_type' => 'artist',
                  'post_status'    => 'publish',
                  'posts_per_page' => $artists_amount ? intval($artists_amount) : 10,
                  'orderby' => 'rand',
                  'meta_query' => [
                    [
                      'key' => 'image',
                      'compare' => 'EXISTS',
                    ],
                  ],
                ]);
                if ($marquee_artists->have_posts()): ?>
                    <div class="itemsContainer">
                      <?php while ($marquee_artists->have_posts()): $marquee_artists->the_post();
                        $artist_name = get_the_title();
                        $artist_category = get_field('category');
                        $artist_link = get_permalink();
                        $artist_image = get_field('image', get_the_ID());
                        $artist_id = get_the_ID();
                      ?>
                      <a class="artistMarqueeItem item" title="Book <?= esc_html($artist_name) ?>" href="<?= esc_url(home_url('/booking-request?artist_id=' . $artist_id)) ?>">
                        <span class="artistMarqueeImage">  
                          <img class="lazy" data-src="<?php echo esc_url($artist_image['sizes']['thumbnail']); ?>" alt="<?php echo esc_attr($artist_image['alt'] ?? $artist_name); ?>" />
                        </span>
                          <span class="artistMarqueeInfo">
                            <span class="tinyTitle"><?php echo esc_html($artist_name); ?></br><span class="white">Book&nbsp;<?php echo esc_html($artist_name); ?>&nbsp;now</span></span>
                            <span class="arrows"><i class="icon-arrow-right-up"></i><i class="icon-arrow-right-up"></i></span>
                          </span>
                        </a>
                      <?php endwhile; $marquee_artists->rewind_posts(); ?>
                    </div>
                  <?php endif; ?>
              </div>
          </div>
        <?php endif; ?>
      </div>
    </div>
    <?php endif; ?>
</section>